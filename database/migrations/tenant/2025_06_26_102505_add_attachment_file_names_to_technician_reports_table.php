<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_reports', function (Blueprint $table) {
            // Add attachment_file_names column to store original file names
            // This column stores a JSON array mapping file paths to original file names
            // for better file management and display in the UI
            $table->json('attachment_file_names')->nullable()->after('attachments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_reports', function (Blueprint $table) {
            $table->dropColumn('attachment_file_names');
        });
    }
};
