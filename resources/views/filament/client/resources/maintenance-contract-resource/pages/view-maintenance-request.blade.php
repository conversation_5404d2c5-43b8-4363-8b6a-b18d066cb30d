<x-filament-panels::page>
    {{-- Status Banner --}}
    @php
        $statusConfig = match($record->status) {
            'new' => ['color' => 'warning', 'icon' => 'heroicon-o-clock', 'text' => __('client/resources/maintenance_request.status_options.new')],
            'pending' => ['color' => 'info', 'icon' => 'heroicon-o-eye', 'text' => __('client/resources/maintenance_request.status_options.pending')],
            'assigned' => ['color' => 'primary', 'icon' => 'heroicon-o-user-plus', 'text' => __('client/resources/maintenance_request.status_options.assigned')],
            'in_progress' => ['color' => 'warning', 'icon' => 'heroicon-o-cog-6-tooth', 'text' => __('client/resources/maintenance_request.status_options.in_progress')],
            'completed' => ['color' => 'success', 'icon' => 'heroicon-o-check-badge', 'text' => __('client/resources/maintenance_request.status_options.completed')],
            'canceled' => ['color' => 'gray', 'icon' => 'heroicon-o-x-mark', 'text' => __('client/resources/maintenance_request.status_options.canceled')],
            default => ['color' => 'gray', 'icon' => 'heroicon-o-question-mark-circle', 'text' => $record->status],
        };
    @endphp

    <div class="mb-6">
        <x-filament::section>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                        <x-filament::icon
                            :icon="$statusConfig['icon']"
                            class="w-8 h-8 text-{{ $statusConfig['color'] }}-500"
                        />
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $record->request_number }}
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ __('client/resources/maintenance_request.view.created_on') }} {{ $record->created_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <x-filament::badge
                        :color="$statusConfig['color']"
                        size="lg"
                    >
                        {{ $statusConfig['text'] }}
                    </x-filament::badge>

                    @if($record->contractType)
                        <div class="mt-2">
                            <x-filament::badge color="gray">
                                {{ $record->contractType->name }}
                            </x-filament::badge>
                        </div>
                    @endif
                </div>
            </div>
        </x-filament::section>
    </div>

    {{-- Progress Tracker --}}
    <div class="mb-8">
        <x-filament::section>
            <x-slot name="heading">
                {{ __('client/resources/maintenance_request.view.progress_tracker') }}
            </x-slot>

            <div class="relative">
                <div class="flex items-center justify-between">
                    {{-- Step 1: Request Created --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium text-green-600">{{ __('client/resources/maintenance_request.view.progress.request_created') }}</p>
                            <p class="text-xs text-gray-500">{{ $record->created_at->format('d/m') }}</p>
                        </div>
                    </div>

                    {{-- Progress Line 1 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['pending', 'assigned', 'in_progress', 'completed', 'paid']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 2: Under Review --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']) ? 'bg-green-500 text-white' : ($record->status === 'pending' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'pending')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']) ? 'text-green-600' : ($record->status === 'pending' ? 'text-yellow-600' : 'text-gray-500') }}">{{ __('client/resources/maintenance_request.view.progress.review') }}</p>
                            <p class="text-xs text-gray-500">
                                @if(in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']))
                                    {{ __('client/resources/maintenance_request.view.progress.review_completed') }}
                                @elseif($record->status === 'pending')
                                    {{ __('client/resources/maintenance_request.view.progress.under_review') }}
                                @else
                                    {{ __('client/resources/maintenance_request.view.progress.waiting') }}
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 2 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 3: Technician Assignment --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']) ? 'bg-green-500 text-white' : ($record->status === 'pending' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'pending')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']) ? 'text-green-600' : ($record->status === 'pending' ? 'text-yellow-600' : 'text-gray-500') }}">{{ __('client/resources/maintenance_request.view.progress.technician_assignment') }}</p>
                            <p class="text-xs text-gray-500">
                                @if(in_array($record->status, ['assigned', 'in_progress', 'completed', 'paid']))
                                    {{ __('client/resources/maintenance_request.view.progress.technician_assigned') }}
                                @elseif($record->status === 'pending')
                                    {{ __('client/resources/maintenance_request.view.progress.assigning_technician') }}
                                @else
                                    {{ __('client/resources/maintenance_request.view.progress.waiting') }}
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 3 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['in_progress', 'completed', 'paid']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 4: Work in Progress --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['in_progress', 'completed', 'paid']) ? 'bg-green-500 text-white' : ($record->status === 'assigned' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['in_progress', 'completed', 'paid']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'assigned')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['in_progress', 'completed', 'paid']) ? 'text-green-600' : ($record->status === 'assigned' ? 'text-yellow-600' : 'text-gray-500') }}">{{ __('client/resources/maintenance_request.view.progress.work_execution') }}</p>
                            <p class="text-xs text-gray-500">
                                @if($record->status === 'completed')
                                    {{ __('client/resources/maintenance_request.view.progress.work_completed') }}
                                @elseif($record->status === 'in_progress')
                                    {{ __('client/resources/maintenance_request.view.progress.work_in_progress') }}
                                @elseif($record->status === 'assigned')
                                    {{ __('client/resources/maintenance_request.view.progress.work_starting_soon') }}
                                @else
                                    {{ __('client/resources/maintenance_request.view.progress.waiting') }}
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 4 --}}
                    <div class="flex-1 h-1 mx-4 {{ $record->status === 'completed' ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 5: Completion --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ $record->status === 'completed' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600' }} rounded-full flex items-center justify-center">
                            @if($record->status === 'completed')
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ $record->status === 'completed' ? 'text-green-600' : 'text-gray-500' }}">{{ __('client/resources/maintenance_request.view.progress.completion') }}</p>
                            <p class="text-xs text-gray-500">
                                @if($record->status === 'completed')
                                    {{ __('client/resources/maintenance_request.view.progress.completed') }}
                                @else
                                    {{ __('client/resources/maintenance_request.view.progress.waiting') }}
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>

    {{-- Main Content --}}
    {{ $this->infolist }}

    {{-- Quick Actions Footer --}}
    {{-- Hide this Section for now --}}
    @if(false && in_array($record->status, ['new', 'pending']))
        <div class="mt-8">
            <x-filament::section>
                <x-slot name="heading">
                    {{ __('client/resources/maintenance_request.view.quick_actions') }}
                </x-slot>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-phone" class="w-8 h-8 text-blue-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ __('client/resources/maintenance_request.view.actions.contact_us') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('client/resources/maintenance_request.view.actions.contact_description') }}</p>
                            <x-filament::button
                                wire:click="$dispatch('open-modal', { id: 'contact-support' })"
                                color="primary"
                                size="sm"
                            >
                                {{ __('client/resources/maintenance_request.view.actions.contact_now') }}
                            </x-filament::button>
                        </div>
                    </x-filament::card>

                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-document-text" class="w-8 h-8 text-green-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ __('client/resources/maintenance_request.view.actions.update_data') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('client/resources/maintenance_request.view.actions.update_description') }}</p>
                            <x-filament::button
                                href="{{ route('filament.client.resources.maintenance-contracts.edit', $record) }}"
                                color="success"
                                size="sm"
                            >
                                {{ __('client/resources/maintenance_request.view.actions.edit') }}
                            </x-filament::button>
                        </div>
                    </x-filament::card>

                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-printer" class="w-8 h-8 text-gray-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ __('client/resources/maintenance_request.view.actions.print_request') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('client/resources/maintenance_request.view.actions.print_description') }}</p>
                            <x-filament::button
                                href="{{ route('client.maintenance-request.print', $record) }}"
                                target="_blank"
                                color="gray"
                                size="sm"
                            >
                                {{ __('client/resources/maintenance_request.view.actions.print') }}
                            </x-filament::button>
                        </div>
                    </x-filament::card>
                </div>
            </x-filament::section>
        </div>
    @endif

    {{-- Help Section --}}
    <div class="mt-8">
        <x-filament::section>
            <x-slot name="heading">
                {{ __('client/resources/maintenance_request.view.help.title') }}
            </x-slot>

            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                <div class="flex items-start space-x-4 rtl:space-x-reverse">
                    <x-filament::icon icon="heroicon-o-information-circle" class="w-6 h-6 text-blue-500 mt-1" />
                    <div class="flex-1">
                        <h3 class="font-medium text-blue-900 dark:text-blue-100 mb-2">{{ __('client/resources/maintenance_request.view.help.useful_info') }}</h3>
                        <div class="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                            <p>• <strong>{{ __('client/resources/maintenance_request.view.help.processing_time_label') }}:</strong> {{ __('client/resources/maintenance_request.view.help.processing_time_text') }}</p>
                            <p>• <strong>{{ __('client/resources/maintenance_request.view.help.technician_assignment_label') }}:</strong> {{ __('client/resources/maintenance_request.view.help.technician_assignment_text') }}</p>
                            <p>• <strong>{{ __('client/resources/maintenance_request.view.help.work_execution_label') }}:</strong> {{ __('client/resources/maintenance_request.view.help.work_execution_text') }}</p>
                            <p>• <strong>{{ __('client/resources/maintenance_request.view.help.communication_label') }}:</strong> {{ __('client/resources/maintenance_request.view.help.communication_text') }}</p>
                            <p>• <strong>{{ __('client/resources/maintenance_request.view.help.contract_label') }}:</strong> {{ __('client/resources/maintenance_request.view.help.contract_text') }}</p>
                        </div>

                        <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                            <div class="flex flex-wrap gap-4 text-sm">
                                <a href="tel:+966123456789" class="flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <x-filament::icon icon="heroicon-o-phone" class="w-4 h-4 ml-1" />
                                    123-456-789
                                </a>
                                <a href="mailto:<EMAIL>" class="flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <x-filament::icon icon="heroicon-o-envelope" class="w-4 h-4 ml-1" />
                                    <EMAIL>
                                </a>
                                <span class="flex items-center text-blue-600 dark:text-blue-400">
                                    <x-filament::icon icon="heroicon-o-clock" class="w-4 h-4 ml-1" />
                                    {{ __('client/resources/maintenance_request.view.help.working_hours') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
