<?php

return [
    'fields' => [
        'request_number' => 'Request Number',
        'request_number_help' => 'Request number is generated automatically',
        'contract_type_id' => 'Contract Type',
        'contract_type_help' => 'Select the required contract type',
        'contract_id' => 'Contract',
        'client_id' => 'Client',
        'client_help' => 'Search for client by name, phone, or email',
        'title' => 'Title',
        'title_help' => 'Brief title for the maintenance request',
        'description' => 'Description',
        'description_help' => 'Detailed description of the maintenance request',

        'status' => 'Status',
        'status_help' => 'Current status of the maintenance request',
        'request_date' => 'Request Date',
        'completion_date' => 'Completion Date',
        'completion_date_help' => 'Expected completion date for the request',
        'assigned_to' => 'Assigned To',
        'assigned_to_help' => 'Technician responsible for executing the request',
        'notes' => 'Notes',
        'notes_help' => 'Additional notes about the maintenance request',
        'contract_price' => 'Contract Price',
        'contract_price_help' => 'Specified price for the maintenance contract',
        'price_notes' => 'Price Notes',
        'client_phone' => 'Client Phone',
        'visits_included' => 'Visits Included',
        'created_at' => 'Created At',
        'completed_at' => 'Actual Completion Date',
        'schedule_visit' => 'Schedule Visit',
        'visit_date' => 'Visit Date',
        'visit_time' => 'Visit Time',
        'visit_notes' => 'Visit Notes',
    ],

    // Pages section
    'pages' => [
        'view' => [
            'title' => 'View Maintenance Request',
            'heading' => 'Maintenance Request Details',
        ],
    ],

    'status_options' => [
        'new' => 'New',
        'pending' => 'Pending',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
    ],

    'columns' => [
        'request_number' => 'Request Number',
        'client' => ['name' => 'Client'],
        'contract' => ['contract_number' => 'Contract Number'],
        'title' => 'Title',
        'status' => 'Status',
        'request_date' => 'Request Date',
        'assignedTechnician' => ['name' => 'Assigned To'],
    ],

    'filters' => [
        'client_id' => 'Client',
        'contract_id' => 'Contract',
        'status' => 'Status',
        'assigned_to' => 'Assigned To',
        'created_from' => 'From Date',
        'created_from_placeholder' => 'Select start date',
        'created_until' => 'To Date',
        'created_until_placeholder' => 'Select end date',
    ],

    'actions' => [
        'view' => 'View',
        'view_modal_heading' => 'View Maintenance Request :number',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'delete_modal_heading' => 'Delete Maintenance Request',
        'delete_modal_description' => 'Are you sure you want to delete this maintenance request? This action cannot be undone.',
        'delete_confirm' => 'Yes, Delete',
        'update_price' => 'Set Price',
        'create_contract' => 'Create Contract',
        'create' => 'Create',
        'create_modal_heading' => 'Create New Maintenance Request',
        'create_another' => 'Create Another',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'refresh_cache' => 'Refresh Data',
    ],

    'bulk_actions' => [
        'assign_technician' => 'Assign Technician',
        'update_status' => 'Update Status',
        'delete' => 'Delete Selected',
    ],

    'sections' => [
        'main_details' => 'Main Details',
        'main_details_description' => 'Basic information for the maintenance request',
        'additional_details' => 'Additional Details',
        'additional_details_description' => 'Additional information and detailed description of the request',
        'request_details' => 'Request Details',
        'client_contract' => 'Client & Contract Information',
        'assignment_financials' => 'Assignment & Financials',
        'assignment_financial' => 'Assignment & Financial',
        'technician_assignment' => 'Technician Assignment',
        'visit_scheduling' => 'Visit Scheduling',
    ],

    'placeholders' => [
        'no_contract' => 'No contract',
        'unassigned' => 'Unassigned',
        'not_set' => 'Not set',
        'not_completed' => 'Not completed yet',
        'add_visit_notes' => 'Add any notes for the visit',
    ],

    'messages' => [
        'request_number_copied' => 'Request number copied',
    ],

    'helpers' => [
        'schedule_visit' => 'Enabling this option will automatically schedule a visit when assigning the technician',
    ],

    'notifications' => [
        'technician_assigned' => 'Technician assigned successfully',
        'technician_assigned_with_visit' => 'Technician assigned and visit scheduled successfully',
        'visit_scheduled_for' => 'Visit scheduled for :date at :time',
        'price_updated' => 'Price updated successfully',
        'price_updated_body' => 'Contract price set to :amount SAR',
        'price_update_failed' => 'Failed to update price',
        'price_update_failed_body' => 'An error occurred while updating the price. Please try again.',
        'contract_created' => 'Contract created successfully',
        'contract_created_body' => 'Contract number :number created successfully',
        'contract_creation_failed' => 'Failed to create contract',
        'contract_creation_failed_body' => 'An error occurred while creating the contract. Please try again.',
        'bulk_assigned' => 'Technician assigned to selected requests',
        'bulk_status_updated' => 'Status updated for selected requests',
        'created_successfully' => 'Maintenance request created successfully',
        'created_successfully_body' => 'Maintenance request number :number created successfully',
        'creation_failed' => 'Failed to create maintenance request',
        'creation_failed_body' => 'An error occurred while creating the maintenance request. Please try again.',
        'updated_successfully' => 'Maintenance request updated successfully',
        'updated_successfully_body' => 'Maintenance request number :number updated successfully',
        'update_failed' => 'Failed to update maintenance request',
        'update_failed_body' => 'An error occurred while updating the maintenance request. Please try again.',
        'cache_refreshed' => 'Data refreshed successfully',
    ],

    'empty_state' => [
        'heading' => 'No maintenance requests',
        'description' => 'Start by creating a new maintenance request.',
    ],

    'global_search' => [
        'client' => 'Client',
        'status' => 'Status',
        'created' => 'Created Date',
    ],

    'untitled' => 'Untitled',

    'pages' => [
        'create' => [
            'title' => 'Create New Maintenance Request',
            'heading' => 'Create Maintenance Request',
            'subheading' => 'Enter details for the new maintenance request',
        ],
        'edit' => [
            'title' => 'Edit Maintenance Request :number',
            'heading' => 'Edit Maintenance Request :number',
            'subheading' => 'Client: :client | Status: :status',
        ],
        'list' => [
            'title' => 'Maintenance Requests',
            'heading' => 'Manage Maintenance Requests',
        ],
        'view' => [
            'title' => 'View Maintenance Request :number',
            'heading' => 'Maintenance Request Details :number',
        ],
    ],

    'tabs' => [
        'all' => 'All',
        'new' => 'New',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
        'overdue' => 'Overdue',
        'notes_details' => 'Notes & Details',
        'activity_timeline' => 'Activity Timeline',
        'financial_summary' => 'Financial Summary',
        'related_records' => 'Related Records',
    ],

    'relations' => [
        'visits' => 'Visits',
        'services' => 'Services',
        'payments' => 'Payments',
        'documents' => 'Documents',
    ],

    'fields' => [
        'request_number' => 'Request Number',
        'request_number_help' => 'Request number is generated automatically',
        'contract_type_id' => 'Contract Type',
        'contract_type_help' => 'Select the required contract type',
        'contract_id' => 'Contract',
        'client_id' => 'Client',
        'client_help' => 'Search for client by name, phone, or email',
        'title' => 'Title',
        'title_help' => 'Brief title for the maintenance request',
        'description' => 'Description',
        'description_help' => 'Detailed description of the maintenance request',
        'technician' => 'Technician',
        'assignment_notes' => 'Assignment Notes',
        'status' => 'Status',
        'status_help' => 'Current status of the maintenance request',
        'request_date' => 'Request Date',
        'completion_date' => 'Completion Date',
        'completion_date_help' => 'Expected completion date for the request',
        'assigned_to' => 'Assigned To',
        'assigned_to_help' => 'Technician responsible for executing the request',
        'notes' => 'Notes',
        'notes_help' => 'Additional notes about the maintenance request',
        'contract_price' => 'Contract Price',
        'contract_price_help' => 'Specified price for the maintenance contract',
        'price_notes' => 'Price Notes',
        'client_phone' => 'Client Phone',
        'visits_included' => 'Visits Included',
        'created_at' => 'Created At',
        'completed_at' => 'Actual Completion Date',
        'assigned_technician' => 'Assigned Technician',
        'price' => 'Price',
        'payment_status' => 'Payment Status',
        'contract_period' => 'Contract Period',
        'contract_start_date' => 'Contract Start Date',
        'contract_end_date' => 'Contract End Date',
        'contract_type_description' => 'Contract Type Description',
        'contract_benefits' => 'Contract Benefits',
        'total_amount_required' => 'Total Required Amount',
        'amount_paid' => 'Amount Paid',
        'amount_remaining' => 'Remaining Amount',
        'payment_instructions' => 'Payment Instructions',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'contract_terms' => 'Contract Terms',
        'contract_status' => 'Contract Status',
    ],

    'currency' => [
        'sar' => 'SAR',
    ],

    'payment_status' => [
        'pending' => 'pending',
        'paid' => 'paid',
        'overdue' => 'overdue',
        'no_payments' => 'No payments',
        'unpaid' => 'Unpaid',
        'partially_paid' => 'Partially paid',
        'fully_paid' => 'Fully paid',
    ],

    'contract_status' => [
        'pending' => 'Pending',
        'active' => 'Active',
        'expired' => 'Expired',
        'cancelled' => 'Cancelled',
    ],

    'messages' => [
        'request_number_copied' => 'Request number copied',
        'activity_timeline_placeholder' => 'Activity timeline will be displayed here.',
    ],

    'wizard' => [
        'step1' => [
            'title' => 'Select Contract Type',
            'heading' => 'Choose Maintenance Contract Type',
            'description' => 'Select the contract type that suits your needs',
            'section_title' => 'Available Contract Types',
            'section_description' => 'Select the contract type that suits your needs',
            'contract_types_title' => 'Available Contract Types',
            'next_button' => 'Next',
            'loading' => 'Loading...',
            'messages' => [
                'error_title' => 'Error',
                'invalid_contract_type' => 'The selected contract type is invalid',
                'please_select_contract_type' => 'Please select a contract type',
                'contract_selected_title' => 'Contract Type Selected',
                'contract_selected_body' => 'Selected: :name',
            ],
        ],

        'step2' => [
            'title' => 'Contract Details',
            'heading' => 'Enter Contract Details',
            'description' => 'Fill in the required information for the maintenance contract',
            'section_title' => 'Company and Contact Information',
            'section_description' => 'Enter your company details and contact information',
            'previous_button' => 'Previous',
            'next_button' => 'Next',
            'loading' => 'Loading...',

            'fields' => [
                'company_name' => 'Company Name',
                'company_name_placeholder' => 'Full institution name',
                'contact_name' => 'Contact Name',
                'contact_name_placeholder' => 'Full name of contact person',
                'phone' => 'Phone Number',
                'phone_placeholder' => '05xxxxxxxx',
                'email' => 'Email Address',
                'email_placeholder' => '<EMAIL>',
                'address' => 'Address',
                'address_placeholder' => 'Detailed address',
            ],
        ],

        'step3' => [
            'title' => 'Review and Confirm',
            'heading' => 'Review Contract Details',
            'description' => 'Review all details before submitting the request',
            'previous_button' => 'Previous',
            'submit_button' => 'Submit Request',
            'loading' => 'Submitting...',
            'contract_summary_title' => 'Contract Summary',
            'summary_section_title' => 'Contract Summary',
            'terms_section_title' => 'Terms and Conditions',

            'summary' => [
                'duration_unit' => 'months',
            ],
            'messages' => [
                'request_submitted_title' => 'Request Submitted Successfully',
                'request_submitted_body' => 'Request Number: :number',
            ],
            'fields' => [
                'terms_agreement' => 'I agree to the terms and conditions',
                'terms_agreement_helper' => 'Click here to view terms and conditions',
            ],
        ],

        'success' => [
            'title' => 'Request Created Successfully',
            'heading' => 'Maintenance Request Created Successfully!',
            'step_heading' => 'Request Created Successfully',
            'step_description' => 'Your maintenance request has been submitted successfully',
            'main_heading' => 'Maintenance Request Created Successfully!',
            'request_number_label' => 'Request Number',
            'current_status_label' => 'Current Status',
            'next_steps_title' => 'Next Steps',
            'details_section_title' => 'Request Details',
            'timeline_title' => 'Expected Timeline',

            'status_descriptions' => [
                'new' => 'Your request has been created successfully and is now in the queue for review. We will contact you soon to confirm the details.',
                'pending' => 'Your request is being reviewed by our specialized team.',
                'assigned' => 'A specialized technician has been assigned to your request and will contact you soon.',
                'in_progress' => 'Work is in progress on your request.',
                'completed' => 'Your request has been completed successfully.',
                'canceled' => 'Your request has been canceled.',
                'default' => 'Request status: :status',
            ],

            'next_steps' => [
                'new' => 'Our team will review your request within 1-3 business days and will contact you to confirm details and schedule the visit.',
                'pending' => 'We will contact you within 24 hours to confirm the details.',
                'assigned' => 'The assigned technician will contact you to schedule a suitable visit time.',
                'in_progress' => 'Work will be completed according to the agreed timeline.',
                'completed' => 'Thank you for choosing our services.',
                'default' => 'We will contact you soon regarding your request.',
            ],

            'actions' => [
                'new_request' => 'New Request',
                'view_requests' => 'View My Requests',
                'back_to_dashboard' => 'Back to Dashboard',
            ],

            'details' => [
                'request_number' => 'Request Number',
                'status' => 'Status',
                'created_date' => 'Created Date',
                'contract_type' => 'Contract Type',
                'visits_included' => 'Visits Included',
                'client' => 'Client',
                'not_specified' => 'Not specified',
            ],
        ],
    ],

    // Cards section
    'cards' => [
        'status' => 'Request Status',
        'request_number' => 'Request Number',
        'contract_type' => 'Contract Type',
        'creation_date' => 'Creation Date',
    ],

    // Messages section
    'messages' => [
        'request_number_copied' => 'Request number copied',
        'phone_copied' => 'Phone number copied',
        'email_copied' => 'Email address copied',
        'contract_number_copied' => 'Contract number copied',
        'payment_instructions' => 'Payment details will be sent via email or phone after request approval.',
        'activity_timeline_placeholder' => 'Activity timeline will be displayed here.',
        'generating_pdf' => 'Generating PDF...',
        'please_wait' => 'Please wait, this process may take a few seconds',
        'pdf_generation_default_error' => 'Please check DocKing settings and template content',
    ],

    // Additional sections
    'sections' => [
        'request_details' => 'Request Details',
        'client_contract' => 'Client & Contract Information',
        'assignment_financial' => 'Assignment & Financial',
        'progress_timeline' => 'Request Progress Timeline',
        'additional_info' => 'Additional Information',
        'contract_details' => 'Contract Details',
    ],

    // Additional fields
    'fields' => [
        'request_number' => 'Request Number',
        'request_number_help' => 'Request number is generated automatically',
        'contract_type_id' => 'Contract Type',
        'contract_type_help' => 'Select the required contract type',
        'contract_id' => 'Contract',
        'client_id' => 'Client',
        'client_help' => 'Search for client by name, phone, or email',
        'title' => 'Title',
        'title_help' => 'Brief title for the maintenance request',
        'description' => 'Description',
        'description_help' => 'Detailed description of the maintenance request',
        'technician' => 'Technician',
        'assignment_notes' => 'Assignment Notes',
        'status' => 'Status',
        'status_help' => 'Current status of the maintenance request',
        'request_date' => 'Request Date',
        'completion_date' => 'Completion Date',
        'completion_date_help' => 'Expected completion date for the request',
        'assigned_to' => 'Assigned To',
        'assigned_to_help' => 'Technician responsible for executing the request',
        'notes' => 'Notes',
        'notes_help' => 'Additional notes about the maintenance request',
        'contract_price' => 'Contract Price',
        'contract_price_help' => 'Specified price for the maintenance contract',
        'price_notes' => 'Price Notes',
        'client_phone' => 'Client Phone',
        'visits_included' => 'Visits Included',
        'created_at' => 'Created At',
        'completed_at' => 'Actual Completion Date',
        'assigned_technician' => 'Assigned Technician',
        'price' => 'Price',
        'payment_status' => 'Payment Status',
        'contract_period' => 'Contract Period',
        'contract_start_date' => 'Contract Start Date',
        'contract_end_date' => 'Contract End Date',
        'contract_type_description' => 'Contract Type Description',
        'contract_benefits' => 'Contract Benefits',
        'total_amount_required' => 'Total Amount Required',
        'amount_paid' => 'Amount Paid',
        'amount_remaining' => 'Amount Remaining',
        'payment_instructions' => 'Payment Instructions',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'contract_terms' => 'Contract Terms',
        'contract_status' => 'Contract Status',
        'client_email' => 'Client Email',
        'contract_number' => 'Contract Number',
        'contract_type' => 'Contract Type',
        'schedule_visit' => 'Schedule Visit',
        'visit_date' => 'Visit Date',
        'visit_time' => 'Visit Time',
        'visit_notes' => 'Visit Notes',
    ],

    // Placeholders section
    'placeholders' => [
        'no_title' => 'No title',
        'no_notes' => 'No notes',
        'not_specified' => 'Not specified',
        'no_contract' => 'No contract',
        'not_assigned' => 'Not assigned yet',
        'not_priced' => 'Price not set yet',
        'no_payments' => 'No payments',
        'not_set' => 'Not set',
        'no_notes_available' => 'No notes available',
        'no_description_available' => 'No description available',
        'no_benefits_specified' => 'No benefits specified',
        'not_set_yet' => 'Not set yet',
        'no_contract_created' => 'No contract created',
        'no_terms_set' => 'No terms set',
    ],

    // Units section
    'units' => [
        'months' => 'months',
        'riyal' => 'SAR',
    ],

    // Timeline section
    'timeline' => [
        'request_created' => 'Request Created',
        'under_review' => 'Under Review',
        'review_completed' => 'Review Completed',
        'awaiting_review' => 'Awaiting Review',
        'technician_assigned' => 'Technician Assigned',
        'awaiting_assignment' => 'Awaiting Assignment',
        'work_in_progress' => 'Work in Progress',
        'awaiting_work_start' => 'Awaiting Work Start',
        'contract_created' => 'Contract Created',
        'contract_number' => 'Contract Number',
        'awaiting_contract_creation' => 'Awaiting Contract Creation',
        'contract_creation' => 'Contract Creation',
        'processing_completed' => 'Processing Completed',
        'processing_completion' => 'Processing Completion',
        'awaiting_final_procedures' => 'Awaiting Final Procedures',
    ],

    // Tabs section
    'tabs' => [
        'all' => 'All',
        'new' => 'New',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
        'overdue' => 'Overdue',
        'notes_details' => 'Notes & Details',
        'payments' => 'Payments',
        'linked_contract' => 'Linked Contract',
        'activity_timeline' => 'Activity Timeline',
        'financial_summary' => 'Financial Summary',
        'related_records' => 'Related Records',
    ],

    // Actions section
    'actions' => [
        'print_request' => 'Print Request',
        'download_pdf' => 'Download PDF',
        'close' => 'Close',
        'download_contract' => 'Download Contract',
        'contact_support' => 'Contact Support',
        'cancel_request' => 'Cancel Request',
    ],

    // Tooltips section
    'tooltips' => [
        'print_request' => 'Generate and print maintenance request PDF',
        'download_contract' => 'Download contract document',
        'contact_support' => 'Contact support team for assistance',
        'cancel_request' => 'Cancel this maintenance request',
    ],

    // Support section
    'support' => [
        'inquiry_type_label' => 'Inquiry Type',
        'inquiry_types' => [
            'status' => 'Request Status',
            'payment' => 'Payment Issues',
            'contract' => 'Contract Questions',
            'technical' => 'Technical Support',
            'other' => 'Other',
        ],
        'inquiry_type_placeholder' => 'Select inquiry type',
        'message_label' => 'Message',
        'message_placeholder' => 'Please describe your inquiry in detail...',
        'message_helper' => 'Minimum 10 characters, maximum 1000 characters',
    ],

    // Cancel section
    'cancel' => [
        'modal_heading' => 'Cancel Maintenance Request',
        'modal_description' => 'Are you sure you want to cancel this maintenance request? This action cannot be undone.',
        'submit_label' => 'Yes, Cancel Request',
        'cancel_label' => 'Keep Request',
    ],

    // Notifications section
    'notifications' => [
        'pdf_loaded' => 'PDF Loaded',
        'pdf_loaded_body' => 'Existing PDF document has been loaded successfully.',
        'pdf_generated' => 'PDF Generated Successfully',
        'pdf_generated_body' => 'The maintenance request PDF has been generated and saved.',
        'pdf_generated_temp' => 'PDF Generated (Temporary)',
        'pdf_storage_failed' => 'PDF was generated but could not be saved permanently. You can still download it.',
        'pdf_generation_failed' => 'PDF Generation Failed',
        'pdf_generation_failed_body' => 'An error occurred while generating the PDF. Please try again.',
        'support_sent_title' => 'Support Request Sent',
        'support_sent_body' => 'Your support request has been submitted successfully. We will contact you soon.',
        'support_failed_title' => 'Support Request Failed',
        'support_failed_body' => 'Failed to submit support request. Please try again or contact us directly.',
        'cancel_success_title' => 'Request Canceled',
        'cancel_success_body' => 'The maintenance request has been canceled successfully.',
        'cancel_failed_title' => 'Cancellation Failed',
        'cancel_failed_body' => 'Failed to cancel the maintenance request. Please try again.',
    ],

    // Accessibility (ARIA) section
    'aria' => [
        'progress_timeline' => 'Request progress timeline',
        'notes' => 'Request notes',
        'contract_type_description' => 'Contract type description',
        'contract_benefits' => 'Contract benefits',
        'notes_details_tab' => 'Notes and details tab',
        'total_amount_required' => 'Total amount required',
        'amount_paid' => 'Amount paid',
        'amount_remaining' => 'Amount remaining',
        'payment_instructions' => 'Payment instructions',
        'payments_tab' => 'Payments tab',
        'contract_number' => 'Contract number',
        'contract_start_date' => 'Contract start date',
        'contract_end_date' => 'Contract end date',
        'contract_status' => 'Contract status',
        'contract_terms' => 'Contract terms',
        'linked_contract_tab' => 'Linked contract tab',
        'generate_pdf' => 'Generate PDF document',
        'download_contract' => 'Download contract document',
        'contact_support' => 'Contact support team',
    ],
];
