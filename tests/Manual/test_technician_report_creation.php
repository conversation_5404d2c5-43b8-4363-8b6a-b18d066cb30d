<?php

/**
 * Test technician report creation to verify attachment_file_names column
 * 
 * This script tests if we can create a technician report with attachment_file_names
 * Run with: php tests/Manual/test_technician_report_creation.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\TechnicianReport;
use App\Models\Tenant;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TECHNICIAN REPORT CREATION TEST ===\n\n";

try {
    // Get the first tenant
    $tenant = Tenant::first();
    
    if (!$tenant) {
        echo "❌ No tenant found in the database\n";
        exit(1);
    }
    
    echo "🔍 Found tenant: {$tenant->id}\n\n";
    
    // Initialize tenancy for this tenant
    tenancy()->initialize($tenant);
    
    echo "✅ Tenancy initialized\n\n";
    
    // Test creating a technician report with attachment_file_names
    echo "🧪 Testing technician report creation...\n\n";
    
    $testData = [
        'maintenance_request_id' => 1, // Assuming there's at least one maintenance request
        'technician_id' => 1, // Assuming there's at least one user/technician
        'status' => 'draft',
        'work_summary' => ['ar' => 'ملخص العمل التجريبي'],
        'attachments' => ['test_file.pdf'],
        'attachment_file_names' => ['test_file.pdf' => 'Original Test File.pdf'],
        'drawing_data' => null,
    ];
    
    echo "📋 Test data:\n";
    foreach ($testData as $key => $value) {
        if (is_array($value)) {
            $displayValue = json_encode($value);
        } elseif (is_string($value) && strlen($value) > 50) {
            $displayValue = substr($value, 0, 50) . '...';
        } else {
            $displayValue = $value;
        }
        echo "   - {$key}: " . ($displayValue ?? 'NULL') . "\n";
    }
    echo "\n";
    
    // Try to create the technician report
    try {
        $report = TechnicianReport::create($testData);
        
        echo "✅ Technician report created successfully!\n";
        echo "   - ID: {$report->id}\n";
        echo "   - Status: {$report->status}\n";
        echo "   - Attachments: " . ($report->attachments ? json_encode($report->attachments) : 'NULL') . "\n";
        echo "   - Attachment File Names: " . ($report->attachment_file_names ? json_encode($report->attachment_file_names) : 'NULL') . "\n";
        echo "   - Created At: {$report->created_at}\n\n";
        
        // Test updating the record
        echo "🔄 Testing update with attachment_file_names...\n";
        
        $report->update([
            'attachment_file_names' => json_encode([
                'test_file.pdf' => 'Updated Original Test File.pdf',
                'another_file.jpg' => 'Another Test Image.jpg'
            ])
        ]);
        
        $report->refresh();
        echo "✅ Update successful!\n";
        echo "   - Updated Attachment File Names: " . json_encode($report->attachment_file_names) . "\n\n";
        
        // Clean up - delete the test record
        echo "🧹 Cleaning up test record...\n";
        $report->delete();
        echo "✅ Test record deleted\n\n";
        
    } catch (\Illuminate\Database\QueryException $e) {
        echo "❌ Database error during technician report creation:\n";
        echo "   Error Code: " . $e->getCode() . "\n";
        echo "   Error Message: " . $e->getMessage() . "\n";
        echo "   SQL State: " . ($e->errorInfo[0] ?? 'Unknown') . "\n\n";
        
        if (strpos($e->getMessage(), 'attachment_file_names') !== false) {
            echo "🔍 This confirms the attachment_file_names column issue!\n";
            echo "   The migration may not have been applied correctly.\n";
            echo "   Please run: php artisan tenants:migrate\n\n";
        }
        
    } catch (Exception $e) {
        echo "❌ General error during technician report creation:\n";
        echo "   Error: " . $e->getMessage() . "\n";
        echo "   File: " . $e->getFile() . "\n";
        echo "   Line: " . $e->getLine() . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during tenant initialization: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
}

echo "=== TEST COMPLETE ===\n";
