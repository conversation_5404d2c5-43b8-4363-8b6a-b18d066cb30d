<?php

/**
 * Verify technician_reports table structure and attachment_file_names column
 * 
 * This script checks if the attachment_file_names column exists in the technician_reports table
 * Run with: php tests/Manual/verify_technician_reports_table.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Stancl\Tenancy\Database\Models\Tenant;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TECHNICIAN REPORTS TABLE VERIFICATION ===\n\n";

try {
    // Get the first tenant
    $tenant = Tenant::first();
    
    if (!$tenant) {
        echo "❌ No tenant found in the database\n";
        exit(1);
    }
    
    echo "🔍 Found tenant: {$tenant->id}\n";

    // Get domains safely
    try {
        $domains = $tenant->domains ? $tenant->domains->pluck('domain')->toArray() : [];
        echo "🔍 Tenant domains: " . (empty($domains) ? 'None' : implode(', ', $domains)) . "\n\n";
    } catch (Exception $e) {
        echo "🔍 Tenant domains: Could not retrieve\n\n";
    }
    
    // Switch to tenant context
    $tenant->run(function () {
        echo "📋 Checking technician_reports table structure...\n\n";
        
        // Check if table exists
        if (!Schema::hasTable('technician_reports')) {
            echo "❌ technician_reports table does not exist!\n";
            echo "   This means the tenant migrations haven't been run properly.\n";
            echo "   Please run: php artisan tenants:migrate\n";
            return;
        }
        
        echo "✅ technician_reports table exists\n\n";
        
        // Get all columns
        $columns = Schema::getColumnListing('technician_reports');
        echo "📊 Table columns (" . count($columns) . " total):\n";
        foreach ($columns as $column) {
            echo "   - {$column}\n";
        }
        
        echo "\n";
        
        // Check specifically for attachment_file_names column
        if (in_array('attachment_file_names', $columns)) {
            echo "✅ attachment_file_names column exists!\n";
            
            // Get column details
            try {
                $columnType = DB::select("SELECT data_type, is_nullable, column_default 
                                        FROM information_schema.columns 
                                        WHERE table_name = 'technician_reports' 
                                        AND column_name = 'attachment_file_names'");
                
                if (!empty($columnType)) {
                    $col = $columnType[0];
                    echo "   📋 Column details:\n";
                    echo "      - Type: {$col->data_type}\n";
                    echo "      - Nullable: {$col->is_nullable}\n";
                    echo "      - Default: " . ($col->column_default ?? 'NULL') . "\n";
                }
            } catch (Exception $e) {
                echo "   ⚠️  Could not get column details: " . $e->getMessage() . "\n";
            }
            
        } else {
            echo "❌ attachment_file_names column is missing!\n";
            echo "   The migration may not have been applied correctly.\n";
            echo "   Please run: php artisan tenants:migrate\n";
        }
        
        echo "\n";
        
        // Check for other important columns
        $requiredColumns = ['id', 'maintenance_request_id', 'technician_id', 'status', 'attachments', 'drawing_data'];
        echo "🔍 Checking for other required columns:\n";
        
        foreach ($requiredColumns as $requiredColumn) {
            if (in_array($requiredColumn, $columns)) {
                echo "   ✅ {$requiredColumn}\n";
            } else {
                echo "   ❌ {$requiredColumn} (missing)\n";
            }
        }
        
        echo "\n";
        
        // Test if we can query the table
        try {
            $count = DB::table('technician_reports')->count();
            echo "📊 Current records in technician_reports: {$count}\n";
        } catch (Exception $e) {
            echo "❌ Error querying technician_reports table: " . $e->getMessage() . "\n";
        }
    });
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
}

echo "\n=== VERIFICATION COMPLETE ===\n";
